using System;
using System.Collections.Generic;
using Items;
using Systems;
using Systems.Services;
using UI.ContextMenu;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.UIElements;

namespace UI
{
    public class InventoryItemElement : ClickableElement, IContextMenuProvider
    {
        private const int SLOT_SIZE = 60;
        private const int ITEM_PADDING = 2;
        private const int GRID_BORDER_SIZE = 1;
        private const float MAXIMUM_DRAG_EVENT_INTERVAL = 0.025f;
    
        private float lastDragEventTime = 0f;
        private bool isBeingDragged;
    
        private Item _item;
        public Item Item
        {
            get { return _item; }
        }

        private ContainerWindow mostRecentlyHoveredContainerWindow;
        private ContainerWindow currentContainerWindow;
        public ContainerWindow CurrentContainerWindow
        {
            get => currentContainerWindow;
            private set
            {
                if (Equals(value, currentContainerWindow))
                    return;
            
                currentContainerWindow = value;
            }
        }

        private Vector2 screenPosition;
        private VisualElement previousParentElement;
        private Vector2Int previousInventoryCoordinates;

        private VisualElement _itemImage;
        private TextElement _itemStackCountText;

        public TextElement ItemStackCountText
        {
            get => _itemStackCountText;
            set
            {
                _itemStackCountText = value;
            }
        }

        private ContainerManager _containerManager;

        public ContainerManager ContainerManager
        {
            get
            {
                if (_containerManager == null)
                    _containerManager = ServiceLocator.Locate<ContainerManager>();

                return _containerManager;
            }
        }
        
        private NotificationSystem _notificationSystem;

        public List<ContextMenuItemData> ContextMenuItems { get; private set; }

        private TemplateContainer _inventoryItemElement;
        private readonly DraggableItemManipulator _draggableItemManipulator;
        private UIManager _uiManager;

        public InventoryItemElement(Item item, ContainerWindow initialContainerWindow):base(item.ItemName)
        {
            _uiManager = ServiceLocator.Locate<UIManager>();
            if (_uiManager == null)
            {
                Debug.LogError("UIManager, InventoryWindowAsset, or InventorySlotAsset is missing!");
                return;
            }
            
            _notificationSystem = ServiceLocator.Locate<NotificationSystem>();
        
            _inventoryItemElement = _uiManager.InventoryItemAsset.Instantiate();

            CurrentContainerWindow = initialContainerWindow;

            _item = item;
            Add(_inventoryItemElement);

            _inventoryItemElement.name = $"{Item.ItemName}";
            _inventoryItemElement.style.width = new StyleLength(Length.Percent(100));
            _inventoryItemElement.style.height = new StyleLength(Length.Percent(100));

            // Set size based on slot dimensions
            style.position = Position.Absolute;
            style.width = Item.ItemSize.x * SLOT_SIZE;
            style.height = Item.ItemSize.y * SLOT_SIZE;

            // Add draggable functionality
            _draggableItemManipulator = new DraggableItemManipulator(this);
            this.AddManipulator(_draggableItemManipulator);

            _itemImage = _inventoryItemElement.Q("Image");
            _itemImage.style.backgroundImage = new StyleBackground(item.ItemData.ItemImage.texture);
            ItemStackCountText = _inventoryItemElement.Q<TextElement>("StackCount");

            _draggableItemManipulator.DragInitiated += OnDragStarted;
            _draggableItemManipulator.DragUpdated += OnDragUpdated;
            _draggableItemManipulator.PointerUp += OnPointerUp;
        
            _uiManager.AddToLayer(this, UILayer.InventoryItem);

            Item.ItemPlaced += OnItemAutoPlaced;
            Item.ItemDiscarded += OnItemDiscarded;

            if (Item is StackableItem stackableItem)
            {
                stackableItem.StackQuantityUpdated += StackQuantityUpdated;
                SetStackCountText(stackableItem.StackQuantity);
            }
        
            PropagateCreationOfContextMenuItems();
        }

        public void PropagateCreationOfContextMenuItems()
        {
            CreateContextMenuItems();
        }

        protected override void OnControlClickPerformed()
        {
            QuickTransferItemToActiveWindow();
        }
        
        protected override void OnDoubleClickPerformed()
        {
            if (Item.ItemData is ContainerItemData)
            {
                OpenContainer(Item.GUID);
            }
            else
            {
                PrintItemID(null);
                PrintGUID(null);
            }
        }

        private void CreateContextMenuItems()
        {
            ContextMenuItems = new List<ContextMenuItemData>();

            var contextItemPrintItemID = new ContextMenuItemData("Debug/Print Item ID", PrintItemID);
            ContextMenuItems.Add(contextItemPrintItemID);

            var contextItemPrintGUID = new ContextMenuItemData("Debug/Print GUID", PrintGUID);
            ContextMenuItems.Add(contextItemPrintGUID);

            if (Item.ItemData is ContainerItemData)
            {
                var contextOpenContainerItem = new ContextMenuItemData("Open", OpenContainer);
                ContextMenuItems.Add(contextOpenContainerItem);
            }

            var contextItemDiscardItem = new ContextMenuItemData("Discard", DiscardItem);
            ContextMenuItems.Add(contextItemDiscardItem);
        }

        private void OpenContainer(DropdownMenuAction obj)
        {
            if (Item is not ContainerItem containerItem)
            {
                Debug.LogError($"Attempted to open container, but the Item was not of type {typeof(ContainerItem)}");
                return;
            }

            OpenContainer(containerItem.GUID);
        }

        private async void OpenContainer(Guid containerGuid)
        {
            try
            {
                await EndOfFrameAwaiter.WaitForEndOfFrameAsync();
            
                UIManager.GetContainerWindow(containerGuid).Open();
            }
            catch (Exception e)
            {
                throw; // TODO handle exception
            }
        }

        private void PrintItemID(DropdownMenuAction obj)
        {
            _notificationSystem.ShowNotification($"{Item.ItemData.ItemID}");
        }

        private void PrintGUID(DropdownMenuAction obj)
        {
            _notificationSystem.ShowNotification($"{Item}");
        }

        private void DiscardItem(DropdownMenuAction obj)
        {
            Item.Discard();
        }

        private void OnItemDiscarded(Item item)
        {
            _item = null;
            RemoveFromHierarchy();
        }

        private void OnItemAutoPlaced(ItemPlacedResult itemPlacedResult)
        {
            var slotAtContainerCoordinates = itemPlacedResult.ContainerWindow.GetSlotAtContainerCoordinates(itemPlacedResult.ContainerCoordinates);
        
            if(slotAtContainerCoordinates == null)
                ReturnToStartOfDrag();
        
            SetPosition(slotAtContainerCoordinates);
        }

        private void StackQuantityUpdated(int stackQuantity)
        {
            SetStackCountText(stackQuantity);
        }

        private void OnDragStarted()
        {
            previousParentElement = parent;
            MoveToItemLayer();

            previousInventoryCoordinates = CurrentContainerWindow.GetSlotCoordinatesOfItem(Item);

            UIManager.ItemBeingDragged = this;
        }

        private void OnDragUpdated(Vector2 pointerPosition)
        {
            var currentTime = Time.realtimeSinceStartup;
            if (currentTime - lastDragEventTime < MAXIMUM_DRAG_EVENT_INTERVAL)
                return;

            isBeingDragged = true;
            lastDragEventTime = currentTime;
        
            UpdateMouseHover(pointerPosition);
        }

        private void UpdateMouseHover(Vector2 pointerPosition)
        {
            var uiWindow = _uiManager.GetUIWindowAt(pointerPosition);

            if (mostRecentlyHoveredContainerWindow != null)
            {
                mostRecentlyHoveredContainerWindow.SetAllSlotsInactive();
                mostRecentlyHoveredContainerWindow = null;
            }

            if (uiWindow is not ContainerWindow inventoryWindow)
            {
                CurrentContainerWindow.SetAllSlotsInactive();
                return;
            }

            mostRecentlyHoveredContainerWindow = inventoryWindow;
            PropagateMouseToContainerWindow(inventoryWindow, pointerPosition);
        }

        private void OnPointerUp(PointerUpEvent pointerUpEvent)
        {
            PropagateMouseReleaseToContainerWindow(CurrentContainerWindow);

            if (!isBeingDragged)
                return;
            isBeingDragged = false;
            UIManager.ItemBeingDragged = null;

            var pointerPosition = (Vector2)pointerUpEvent.position;

            var uiWindow = _uiManager.GetUIWindowAt(pointerPosition);
        
            if(uiWindow is not ContainerWindow inventoryWindow)
            {
                ReturnToStartOfDrag();
                return;
            }

            SnapToInventory(inventoryWindow, pointerPosition);
        }

        private void PropagateMouseToContainerWindow(ContainerWindow containerWindow, Vector2 mousePosition)
        {
            var topLeftPosition = GetTopLeftMostSlotPosition(mousePosition);

            containerWindow.PropagateHover(topLeftPosition, Item);
        }

        private void PropagateMouseReleaseToContainerWindow(ContainerWindow containerWindow)
        {
            containerWindow.PropagateMouseRelease();
        }

        private void MoveToItemLayer()
        {
            _uiManager.AddToLayer(this, UILayer.InventoryItem);
        }

        public bool InitialSnapToInventory(ContainerWindow targetContainer, Vector2Int targetSlotCoordinates)
        {
            var inventorySlot = targetContainer.GetSlotAtContainerCoordinates(targetSlotCoordinates);
            var placedSuccessfully = TryInitialPlacement(targetContainer, targetSlotCoordinates, inventorySlot);
        
            if (!placedSuccessfully)
            {
                Debug.Log($"ERROR PLACING {Item.ItemName}");
                return false;
            }

            return true;
        }

        private void SnapToInventory(ContainerWindow targetContainer, Vector2Int targetSlotCoordinates)
        {
            var inventorySlot = targetContainer.GetSlotAtContainerCoordinates(targetSlotCoordinates);

            SnapToInventory(targetContainer, targetSlotCoordinates, inventorySlot);
        }

        private void SnapToInventory(ContainerWindow targetContainer, Vector2 mousePosition)
        {
            var targetSlotPosition = GetTargetSlotPosition(mousePosition);

            var targetSlotCoordinates = targetContainer.GetSlotCoordinatesAt(targetSlotPosition);
            var inventorySlot = targetContainer.GetSlotAtScreenPosition(targetSlotPosition);
        
            if(inventorySlot == null)
            {
                ReturnToStartOfDrag();
                return;
            }
        
            SnapToInventory(targetContainer, targetSlotCoordinates, inventorySlot);
        }

        private void SnapToInventory(ContainerWindow targetContainer, Vector2Int targetSlotCoordinates,
            VisualElement inventorySlot)
        {
            var placedSuccessfully = TryPlaceItem(targetContainer, targetSlotCoordinates, inventorySlot);
        
            if (!placedSuccessfully)
            {
                ReturnToStartOfDrag();
            }
        }

        private bool TryInitialPlacement(ContainerWindow targetContainer, Vector2Int targetSlotCoordinates,
            VisualElement inventorySlot)
        {
            var itemDataIsInSync = targetContainer.VerifyItemPosition(Item, targetSlotCoordinates);

            if (itemDataIsInSync)
            {
                SetPosition(inventorySlot);
                CurrentContainerWindow = targetContainer;
            }

            return itemDataIsInSync;
        }

        private bool TryPlaceItem(ContainerWindow targetContainer, Vector2Int targetSlotCoordinates,
            VisualElement inventorySlot)
        {
            var canPlaceAtTarget = targetContainer.CanPlaceItem(_item, targetSlotCoordinates);

            if (!canPlaceAtTarget)
            {
                // todo: update feedback when more specific Error Handling is implemented
                _notificationSystem.ShowNotification($"Cannot place {Item.ItemName} in {targetContainer.GetName()}");
                return false;
            }
        
            CurrentContainerWindow?.TryRemoveItem(_item);
            var wasPlacedSuccessfully = targetContainer.TryPlaceItem(Item, targetSlotCoordinates);

            if (wasPlacedSuccessfully)
            {
                SetPosition(inventorySlot);
                CurrentContainerWindow = targetContainer;
            }

            return wasPlacedSuccessfully;
        }

        public void QuickTransferItemToActiveWindow()
        {
            if (_uiManager.FocussedUIWindow is not ContainerWindow focussedContainerWindow)
                return;

            TransferToContainerWindow(focussedContainerWindow);
        }

        public void TransferToContainerWindow(ContainerWindow targetContainerWindow)
        {
            if (Item is StackableItem stackableItem)
            {
                var nextMatchingStackable = targetContainerWindow.GetNextMatchingStackable(stackableItem);
                if (nextMatchingStackable != null)
                {
                    stackableItem.CombineInto(nextMatchingStackable);

                    // recursively attempt to place until the stack is empty
                    if (stackableItem.StackQuantity > 0)
                    {
                        TransferToContainerWindow(targetContainerWindow);
                    }
                    
                    return;
                }
            }
            
            if (!targetContainerWindow.CanPlaceItem(Item))
            {
                // give "cannot place" feedback
                return;
            }

            CurrentContainerWindow?.TryRemoveItem(_item);
            var itemPlacedSuccessfully = targetContainerWindow.TryPlaceItem(Item);
            if (!itemPlacedSuccessfully)
            {
                return;
            }

            var slotCoordinates = targetContainerWindow.GetSlotCoordinatesOfItem(Item);
            var slotVisualElement = targetContainerWindow.GetSlotAtContainerCoordinates(slotCoordinates);
            SetPosition(slotVisualElement);
            CurrentContainerWindow = targetContainerWindow;
        }

        private void ReturnToStartOfDrag()
        {
            CurrentContainerWindow?.TryRemoveItem(_item);
            UIManager.SetAllWindowSlotStylesInactive();
            if (previousParentElement == _uiManager.GetLayer(UILayer.InventoryItem))
            {
                MoveToItemLayer();
                style.left = 0;
                style.top = 0;
                return;
            }

            // todo: Might be able to remove this -- I think it's fixed, but I don't have time to test it at the moment
            if (previousInventoryCoordinates.x < 0 || previousInventoryCoordinates.y < 0)
            {
                Debug.Log($"Exiting suspected infinite loop: previousInventoryCoordinates = {previousInventoryCoordinates}");
                return;
            }
        
            SnapToInventory(CurrentContainerWindow, previousInventoryCoordinates);
        }

        private void SetPosition(VisualElement inventorySlot)
        {
            inventorySlot.Add(this);
            RotateVisuals();

            var standardEdgeDistance = 0;
            
            if (!Item.IsUniformSize)
            {
                var sizeDifference = math.abs(Item.ItemSize.x - Item.ItemSize.y);
                var rotatedEdgeDistance = !Item.IsUniformSize ? SLOT_SIZE/2 * sizeDifference + ITEM_PADDING : -SLOT_SIZE;
                rotatedEdgeDistance = Item.IsHorizontalItem ? rotatedEdgeDistance : -rotatedEdgeDistance;
                style.left = Item.IsRotated ? -rotatedEdgeDistance : standardEdgeDistance;
                style.top = Item.IsRotated ? rotatedEdgeDistance : standardEdgeDistance;
            }
            else
            {
                style.left = standardEdgeDistance;
                style.top = standardEdgeDistance;
            }
            
            SendToBack();
        }

        private Vector2 GetTargetSlotPosition(Vector2 mousePosition)
        {
            return GetTopLeftMostSlotPosition(mousePosition);   
        }
    
        private Vector2 GetTopLeftMostSlotPosition(Vector2 pointerPosition)
        {
            var itemSize = Item.RotatedSize;
        
            var extentsX = itemSize.x * SLOT_SIZE / 4.0f;
            var extentsY = itemSize.y * SLOT_SIZE / 4.0f;

            if (itemSize.x == 1)
                extentsX = 0;
            if (itemSize.y == 1)
                extentsY = 0;

            var topLeftPosition = pointerPosition - new Vector2(extentsX, extentsY);
            return topLeftPosition;
        }

        public void Rotate()
        {
            Item.Rotate();
            RotateVisuals();
            UpdateMouseHover(worldBound.center);
        }

        private void RotateVisuals()
        {
            var angle = Item.IsRotated ? -90 : 0;
            style.rotate = new Rotate(new Angle(angle));
        }

        private void SetStackCountText(int quantity)
        {
            var quantityString = quantity <= 1 ? string.Empty : quantity.ToString("N0");
            ItemStackCountText.text = quantityString;
        }

        public override string GetName()
        {
            return Item.ItemName;
        }
    }
}